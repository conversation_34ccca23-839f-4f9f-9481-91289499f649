"use client"

import { useEffect, useRef, forwardRef, useImperativeHandle, useState, useCallback } from "react"
import { cn } from "@/lib/utils"

// Simple loading component
const EditorLoading = () => (
  <div className="border rounded-md min-h-[120px] bg-gray-50 animate-pulse flex items-center justify-center">
    <span className="text-gray-500">Loading editor...</span>
  </div>
)

interface WysiwygEditorProps {
  value?: string
  onChange?: (value: string, html: string) => void
  placeholder?: string
  className?: string
  readOnly?: boolean
  theme?: "snow" | "bubble"
}

export interface WysiwygEditorRef {
  focus: () => void
  blur: () => void
  getEditor: () => any
}

const WysiwygEditor = forwardRef<WysiwygEditorRef, WysiwygEditorProps>(
  ({ value = "", onChange, placeholder, className, readOnly = false, theme = "snow" }, ref) => {
    const quillRef = useRef<any>(null)
    const [isMounted, setIsMounted] = useState(false)
    const [ReactQuill, setReactQuill] = useState<any>(null)
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
      setIsMounted(true)

      // Dynamically import ReactQuill
      const loadQuill = async () => {
        try {
          const quillModule = await import("react-quill")
          await import("react-quill/dist/quill.snow.css")
          setReactQuill(() => quillModule.default)
          setIsLoading(false)
        } catch (error) {
          console.error("Failed to load ReactQuill:", error)
          setIsLoading(false)
        }
      }

      loadQuill()
    }, [])

    useImperativeHandle(ref, () => ({
      focus: () => {
        try {
          if (quillRef.current && typeof quillRef.current.focus === 'function') {
            quillRef.current.focus()
          }
        } catch (error) {
          console.warn('Error focusing WYSIWYG editor:', error)
        }
      },
      blur: () => {
        try {
          if (quillRef.current && typeof quillRef.current.blur === 'function') {
            quillRef.current.blur()
          }
        } catch (error) {
          console.warn('Error blurring WYSIWYG editor:', error)
        }
      },
      getEditor: () => {
        try {
          return quillRef.current?.getEditor?.() || null
        } catch (error) {
          console.warn('Error getting WYSIWYG editor instance:', error)
          return null
        }
      },
    }), [])

    // Quill modules configuration
    const modules = {
      toolbar: [
        [{ header: [1, 2, 3, false] }],
        ["bold", "italic", "underline", "strike"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ indent: "-1" }, { indent: "+1" }],
        [{ align: [] }],
        ["link", "blockquote"],
        [{ color: [] }, { background: [] }],
        ["clean"],
      ],
      clipboard: {
        matchVisual: false,
      },
    }

    // Quill formats configuration
    const formats = [
      "header",
      "bold",
      "italic",
      "underline",
      "strike",
      "list",
      "bullet",
      "indent",
      "align",
      "link",
      "blockquote",
      "color",
      "background",
    ]

    const handleChange = useCallback((content: string, delta: any, source: any, editor: any) => {
      try {
        if (editor && typeof editor.getHTML === 'function' && typeof editor.getText === 'function') {
          const html = editor.getHTML()
          const text = editor.getText()

          if (onChange) {
            onChange(text.trim(), html)
          }
        } else {
          // Fallback if editor methods are not available
          if (onChange) {
            onChange(content || "", content || "")
          }
        }
      } catch (error) {
        console.warn('Error in WYSIWYG editor change handler:', error)
        // Fallback to content as both text and html
        if (onChange) {
          onChange(content || "", content || "")
        }
      }
    }, [onChange])

    if (!isMounted || isLoading || !ReactQuill) {
      return (
        <div className={cn("wysiwyg-editor", className)}>
          <EditorLoading />
        </div>
      )
    }

    return (
      <div className={cn("wysiwyg-editor", className)}>
        <ReactQuill
          ref={quillRef}
          theme={theme}
          value={value || ""}
          onChange={handleChange}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          readOnly={readOnly}
          style={{
            backgroundColor: "white",
          }}
        />
        <style jsx global>{`
          .wysiwyg-editor .ql-editor {
            min-height: 120px;
            font-size: 14px;
            line-height: 1.5;
          }
          
          .wysiwyg-editor .ql-toolbar {
            border-top: 1px solid #e2e8f0;
            border-left: 1px solid #e2e8f0;
            border-right: 1px solid #e2e8f0;
            border-bottom: none;
            border-radius: 6px 6px 0 0;
          }
          
          .wysiwyg-editor .ql-container {
            border-bottom: 1px solid #e2e8f0;
            border-left: 1px solid #e2e8f0;
            border-right: 1px solid #e2e8f0;
            border-top: none;
            border-radius: 0 0 6px 6px;
            font-family: inherit;
          }
          
          .wysiwyg-editor .ql-editor.ql-blank::before {
            color: #9ca3af;
            font-style: normal;
          }
          
          /* Dark mode support */
          .dark .wysiwyg-editor .ql-toolbar {
            border-color: #374151;
            background-color: #1f2937;
          }
          
          .dark .wysiwyg-editor .ql-container {
            border-color: #374151;
            background-color: #1f2937;
          }
          
          .dark .wysiwyg-editor .ql-editor {
            color: #f9fafb;
          }
          
          .dark .wysiwyg-editor .ql-editor.ql-blank::before {
            color: #6b7280;
          }
          
          /* Focus styles */
          .wysiwyg-editor .ql-container.ql-focused,
          .wysiwyg-editor .ql-toolbar.ql-focused {
            border-color: #3b82f6;
          }
          
          /* Ensure proper spacing */
          .wysiwyg-editor .ql-editor p {
            margin-bottom: 0.75rem;
          }
          
          .wysiwyg-editor .ql-editor p:last-child {
            margin-bottom: 0;
          }
          
          .wysiwyg-editor .ql-editor ul,
          .wysiwyg-editor .ql-editor ol {
            margin-bottom: 0.75rem;
          }
          
          .wysiwyg-editor .ql-editor blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
          }
          
          .dark .wysiwyg-editor .ql-editor blockquote {
            border-left-color: #4b5563;
          }
        `}</style>
      </div>
    )
  }
)

WysiwygEditor.displayName = "WysiwygEditor"

export { WysiwygEditor }
